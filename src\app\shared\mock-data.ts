export const MOCK_DATA = {
  latestNews: [
    {
      id: 1,
      pub_date: '19.03.2025',
      title: 'Новая функция в нашем приложении!',
      content: 'Lightweight and user friendly\n' + 'With our commitment to at least four major updates per year, you receive the most up-to-date functionality and new components in addition to monthly service packs and bug fixes. Custom patches are available as needed.',
      imageUrl: 'https://static.tildacdn.pro/tild6633-3161-4061-a531-383261353239/05_EXTERIOR_SIDE.jpg',
      author: 'Иван Петров'
    },
    {
      id: 2,
      pub_date: '19.03.2025',
      title: 'Советы по оптимизации вашего сайта',
      content: 'Узнайте, как улучшить производительность вашего сайта с помощью наших экспертных советов...',
      imageUrl: 'https://static.tildacdn.pro/tild3964-6332-4535-b936-303830366433/Frame_160.png',
      author: 'Анна Сидорова'
    },
    {
      id: 3,
      pub_date: '19.03.2025',
      title: 'Анонс нового курса по программированию',
      content: 'Приглашаем вас на наш новый курс, где вы научитесь создавать веб-приложения с нуля...',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/ad/%D0%9A%D0%B0%D0%BC%D0%BF%D1%83%D1%81_%D0%A3%D0%BD%D0%B8%D0%B2%D0%B5%D1%80%D1%81%D0%B8%D1%82%D0%B5%D1%82%D0%B0_%D0%9D%D0%B0%D1%80%D1%85%D0%BE%D0%B7.jpg/1200px-%D0%9A%D0%B0%D0%BC%D0%BF%D1%83%D1%81_%D0%A3%D0%BD%D0%B8%D0%B2%D0%B5%D1%80%D1%81%D0%B8%D1%82%D0%B5%D1%82%D0%B0_%D0%9D%D0%B0%D1%80%D1%85%D0%BE%D0%B7.jpg',
      author: 'Сергей Кузнецов'
    },
    {
      id: 4,
      pub_date: '19.03.2025',
      title: 'Обновление политики конфиденциальности чтобы обеспечить большую прозрачность бизнеса',
      content: 'Мы обновили нашу политику конфиденциальности, чтобы обеспечить большую прозрачность...',
      imageUrl: 'https://ekonomist.kz/wp-content/uploads/2019/11/%D0%92-%D0%9D%D0%B0%D1%80%D1%85%D0%BE%D0%B7%D0%B5-%D0%BF%D1%80%D0%BE%D0%B9%D0%B4%D0%B5%D1%82-%D0%BE%D1%82%D1%87%D0%B5%D1%82%D0%BD%D0%B0%D1%8F-%D0%B2%D1%81%D1%82%D1%80%D0%B5%D1%87%D0%B0-%D1%81-%D1%83%D1%87%D0%B0%D1%81%D1%82%D0%B8%D0%B5%D0%BC-%D0%A0%D1%83%D0%B1%D0%B5%D0%BD%D0%B0-%D0%92%D0%B0%D1%80%D0%B4%D0%B0%D0%BD%D1%8F%D0%BD%D0%B0.jpg',
      author: 'Елена Смирнова'
    },
    {
      id: 5,
      pub_date: '19.03.2025',
      title: 'Как эффективно использовать социальные сети для бизнеса',
      content: 'Узнайте, как привлечь больше клиентов с помощью социальных сетей...',
      imageUrl: 'https://ekonomist.kz/wp-content/uploads/2019/11/%D0%92-%D0%9D%D0%B0%D1%80%D1%85%D0%BE%D0%B7%D0%B5-%D0%BF%D1%80%D0%BE%D0%B9%D0%B4%D0%B5%D1%82-%D0%BE%D1%82%D1%87%D0%B5%D1%82%D0%BD%D0%B0%D1%8F-%D0%B2%D1%81%D1%82%D1%80%D0%B5%D1%87%D0%B0-%D1%81-%D1%83%D1%87%D0%B0%D1%81%D1%82%D0%B8%D0%B5%D0%BC-%D0%A0%D1%83%D0%B1%D0%B5%D0%BD%D0%B0-%D0%92%D0%B0%D1%80%D0%B4%D0%B0%D0%BD%D1%8F%D0%BD%D0%B0.jpg',
      author: 'Дмитрий Иванов'
    }
  ],
  mockCourses: [
    {
      id: 0,
      title: 'Sviluppo Web Frontend',
      rate: 90,
      description: 'Impara a creare interfacce web moderne e interattive utilizzando HTML, CSS e JavaScript. Approfondisci i framework React e Angular.',
      instructor: 'Mario Rossi',
      duration: '8 settiman2e',
      price: 299,
      banner_image_url: '/assets/icons/learn.svg'
    },
    {
      id: 1,
      title: 'Sviluppo Web Backend con Node.js',
      rate: 92,
      description: 'Веб-разработчик — это специалист, ' +
        'который создает и поддерживает сайты и приложения. Он может работать как над внешним видом сайта,' +
        ' так и над его внутренней, серверной частью. Тестирование и поиск багов — хоть и не основная, но тоже одна из задач веб-программирования.',
      instructor: 'Luisa Bianchi',
      duration: '10 settimane',
      price: 349,
      banner_image_url: '/assets/icons/learn.svg'
    },
    {
      id: 2,
      title: 'Python per Data Science',
      rate: 75,
      description: 'Esplora il mondo della data science con Python. Impara a utilizzare librerie come NumPy, Pandas e Scikit-learn per analisi dati e machine learning.',
      instructor: 'Giuseppe Verdi',
      duration: '12 settimane',
      price: 399,
      banner_image_url: '/assets/icons/learn.svg'
    },
    {
      id: 3,
      title: 'Marketing Digitale Avanzato',
      rate: 35,
      description: 'Padroneggia le strategie di marketing digitale più efficaci. Impara a creare campagne pubblicitarie su Google Ads e Facebook Ads, e a ottimizzare la SEO.',
      instructor: 'Anna Russo',
      duration: '6 settimane',
      price: 249,
      banner_image_url: '/assets/icons/learn.svg'
    },
    {
      id: 4,
      title: 'Design UX/UI',
      rate: 85,
      description: 'Crea esperienze utente coinvolgenti e intuitive. Impara a progettare interfacce utente efficaci utilizzando Figma e Adobe XD.',
      instructor: 'Elena Ferrari',
      duration: '8 settimane',
      price: 299,
      banner_image_url: '/assets/icons/learn.svg'
    },
  ]
};


export const COURSES = [
  {
    id: 1,
    title: 'Введение в программирование',
    description: 'Базовые концепции программирования на Python.',
    banner_image_url: 'https://example.com/banners/python.jpg',
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-02-10T12:00:00Z',
  },
  {
    id: 2,
    title: 'Алгоритмы и структуры данных',
    description: 'Продвинутый курс по алгоритмам и структурам данных.',
    banner_image_url: 'https://example.com/banners/algorithms.jpg',
    created_at: '2024-02-15T11:30:00Z',
    updated_at: '2024-03-01T15:45:00Z',
  },
  {
    id: 3,
    title: 'Веб-разработка ',
    description: 'Создание SPA приложений ',
    banner_image_url: 'https://example.com/banners/angular.jpg',
    created_at: '2024-03-20T09:00:00Z',
    updated_at: '2024-03-25T09:30:00Z',
  },
];

export const PREREQUISITES = [
  {
    course_id: 2,
    prerequisite_course_id: 1,
    created_at: '2024-02-16T10:00:00Z',
    updated_at: '2024-02-16T10:00:00Z',
  },
  {
    course_id: 3,
    prerequisite_course_id: 1,
    created_at: '2024-03-20T10:00:00Z',
    updated_at: '2024-03-20T10:00:00Z',
  },
];

export const THREADS = [
  {
    id: 1,
    course_id: 1,
    semester_id: 1,
    teacher: "Jonhy",
    title: 'Основы синтаксиса Python',
    syllabus_url: 'https://example.com/syllabus/python_basics.pdf',
    schedule: [
      {day: 'Tuesday', time: '10:00 - 13 00'},
      {day: 'Thursday', time: '10:00 - 13 00'}
    ]
  },
  {
    id: 2,
    course_id: 2,
    semester_id: 2,
    teacher: "Jonhy",
    title: 'Сортировки и поиск',
    syllabus_url: 'https://example.com/syllabus/sorting_search.pdf',
    schedule: [
      {day: 'Tuesday', time: '10:00 - 13 00'},
      {day: 'Thursday', time: '10:00 - 13 00'}
    ]
  },
  {
    id: 3,
    course_id: 3,
    semester_id: 2,
    teacher: "Clark",
    title: 'Компоненты в Angular',
    syllabus_url: 'https://example.com/syllabus/angular_components.pdf',
    schedule: [
      {day: 'Tuesday', time: '10:00 - 13 00'},
      {day: 'Thursday', time: '10:00 - 13 00'}
    ]
  },
  {
    id: 4,
    course_id: 3,
    semester_id: 2,
    teacher: "Jonhy",
    title: 'Компоненты в React',
    syllabus_url: 'https://example.com/syllabus/angular_components.pdf',
    schedule: [
      {day: 'Tuesday', time: '10:00 - 13 00'},
      {day: 'Thursday', time: '10:00 - 13 00'}
    ]
  },
  {
    id: 5,
    course_id: 3,
    semester_id: 2,
    teacher: "Max",
    title: 'Java',
    syllabus_url: 'https://example.com/syllabus/angular_components.pdf',
    schedule: [
      {day: 'Tuesday', time: '10:00 - 13 00'},
      {day: 'Thursday', time: '10:00 - 13 00'}
    ]
  },
];
export const REGISTERED_THREADS: { student_id: number; thread_id: number }[] = [];

interface Weeks {
  id: number;
  thread_id: number;
  week_number: number;
  type: string;
  title: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

const mockWeeks: Weeks[] = [
  {
    id: 1,
    thread_id: 1,
    week_number: 1,
    type: 'midterm',
    title: 'Первая неделя семестра',
    description: 'Вводные лекции и первое задание.',
    created_at: '2025-04-10T10:00:00Z',
    updated_at: '2025-04-10T10:00:00Z',
  },
  {
    id: 2,
    thread_id: 1,
    week_number: 2,
    type: 'regular',
    title: 'Тема 1: Основы программирования',
    description: 'Лекция и практическое задание по основам.',
    created_at: '2025-04-11T12:30:00Z',
    updated_at: '2025-04-11T12:30:00Z',
  },
  {
    id: 3,
    thread_id: 1,
    week_number: 3,
    type: 'regular',
    title: 'Введение в машинное обучение',
    description: 'Обзор курса и основные понятия.',
    created_at: '2025-04-12T15:45:00Z',
    updated_at: '2025-04-12T15:45:00Z',
  },
  {
    id: 3,
    thread_id: 1,
    week_number: 4,
    type: 'regular',
    title: 'Введение в машинное обучение',
    description: 'Обзор курса и основные понятия.',
    created_at: '2025-04-12T15:45:00Z',
    updated_at: '2025-04-12T15:45:00Z',
  },
];

interface AssignmentGroups {
  id: number;
  thread_id: number;
  name: string;
  group_type: string;
  weight: number | null;
  parent_group_id: number | null;
  created_at: string;
  updated_at: string;
}

const mockAssignmentGroups: AssignmentGroups[] = [
  {
    id: 1,
    thread_id: 1,
    name: 'Домашние задания',
    group_type: 'regular',
    weight: 0.4,
    parent_group_id: null,
    created_at: '2025-04-10T11:15:00Z',
    updated_at: '2025-04-10T11:15:00Z',
  },
  {
    id: 2,
    thread_id: 1,
    name: 'Контрольные работы',
    group_type: 'midterm',
    weight: 0.6,
    parent_group_id: null,
    created_at: '2025-04-10T11:30:00Z',
    updated_at: '2025-04-10T11:30:00Z',
  },
  {
    id: 3,
    thread_id: 1,
    name: 'Проекты',
    group_type: 'final',
    weight: 1,
    parent_group_id: null,
    created_at: '2025-04-12T16:30:00Z',
    updated_at: '2025-04-12T16:30:00Z',
  },
];

interface Assignments {
  id: number;
  week_id: number;
  title: string;
  description: string | null;
  due_date: string | null;
  max_points: number | null;
  created_at: string;
  updated_at: string;
  assignment_group_id: number | null;
}

const mockAssignments: Assignments[] = [
  {
    id: 1,
    week_id: 1,
    title: 'Первое домашнее задание',
    description: 'Реализовать простую программу на Python.',
    due_date: '2025-04-18T23:59:00Z',
    max_points: 10,
    created_at: '2025-04-10T12:00:00Z',
    updated_at: '2025-04-10T12:00:00Z',
    assignment_group_id: 1,
  },
  {
    id: 2,
    week_id: 2,
    title: 'Контрольная работа №1',
    description: 'Вопросы по основам программирования.',
    due_date: '2025-04-18T18:00:00Z',
    max_points: 25,
    created_at: '2025-04-11T22:00:00Z',
    updated_at: '2025-04-11T14:00:00Z',
    assignment_group_id: 2,
  },
  {
    id: 3,
    week_id: 3,
    title: 'Проект: Классификация изображений',
    description: 'Разработать модель для классификации изображений.',
    due_date: '2025-05-18T23:59:00Z',
    max_points: 100,
    created_at: '2025-04-17T09:00:00Z',
    updated_at: '2025-04-13T09:00:00Z',
    assignment_group_id: 3,
  },
  {
    id: 4,
    week_id: 3,
    title: 'Проект: Классификация изображений',
    description: 'Разработать модель для классификации изображений. Use these Tailwind CSS input group components to create things like search bars with buttons, credit card forms, and other form inputs with combined input and label elements. These components are designed and built by the Tailwind CSS team, and include a variety of different styles and layouts.',
    due_date: '2025-05-15T23:59:00Z',
    max_points: 100,
    created_at: '2025-04-18T09:00:00Z',
    updated_at: '2025-04-13T09:00:00Z',
    assignment_group_id: 3,
  },
  {
    id: 4,
    week_id: 3,
    title: 'Проект: Классификация изображений',
    description: 'Разработать модель для классификации изображений.',
    due_date: '2025-05-15T00:00:00Z',
    max_points: 100,
    created_at: '2025-04-13T09:00:00Z',
    updated_at: '2025-04-13T09:00:00Z',
    assignment_group_id: 3,
  },
  {
    id: 5,
    week_id: 1,
    title: 'Первое домашнее задание',
    description: 'Реализовать простую программу на Python.',
    due_date: '2025-04-19T23:59:00Z',
    max_points: 10,
    created_at: '2025-04-10T12:00:00Z',
    updated_at: '2025-04-10T12:00:00Z',
    assignment_group_id: 1,
  },
  {
    id: 5,
    week_id: 1,
    title: 'Первое домашнее задание',
    description: 'Реализовать простую программу на Python.',
    due_date: '2025-04-16T23:59:00Z',
    max_points: 10,
    created_at: '2025-04-10T12:00:00Z',
    updated_at: '2025-04-10T12:00:00Z',
    assignment_group_id: 1,
  },
];

interface AssignmentAttachments {
  id: number;
  assignment_id: number;
  file_url: string;
  created_at: string;
  updated_at: string;
}

const mockAssignmentAttachments: AssignmentAttachments[] = [
  {
    id: 1,
    assignment_id: 1,
    file_url: 'https://example.com/homework1_instructions.pdf',
    created_at: '2025-04-10T12:15:00Z',
    updated_at: '2025-04-10T12:15:00Z',
  },
  {
    id: 2,
    assignment_id: 2,
    file_url: 'https://example.com/quiz1_questions.docx',
    created_at: '2025-04-11T14:15:00Z',
    updated_at: '2025-04-11T14:15:00Z',
  },
];

interface AssignmentSubmissions {
  id: number;
  assignment_id: number;
  user_id: number;
  submitted_at: string | null;
  file_url: string | null;
  comment: string | null;
  score: number | null;
  feedback: string | null;
  created_at: string;
  updated_at: string;
}

const mockAssignmentSubmissions: AssignmentSubmissions[] = [
  {
    id: 1,
    assignment_id: 1,
    user_id: 1001,
    submitted_at: '2025-04-16T21:30:00Z',
    file_url: 'https://example.com/user1001_homework1.zip',
    comment: 'Мое решение первого домашнего задания.',
    score: 9,
    feedback: 'Хорошая работа, есть небольшие замечания.',
    created_at: '2025-04-16T21:30:00Z',
    updated_at: '2025-04-16T21:30:00Z',
  },
  {
    id: 2,
    assignment_id: 1,
    user_id: 1002,
    submitted_at: '2025-04-17T20:00:00Z',
    file_url: 'https://example.com/user1002_homework1.py',
    comment: 'Программа выполнена.',
    score: 10,
    feedback: 'Отлично!',
    created_at: '2025-04-17T20:00:00Z',
    updated_at: '2025-04-17T20:00:00Z',
  },
  {
    id: 3,
    assignment_id: 2,
    user_id: 1001,
    submitted_at: null,
    file_url: null,
    comment: null,
    score: 22,
    feedback: 'Несколько ошибок в теоретической части.',
    created_at: '2025-04-24T18:15:00Z',
    updated_at: '2025-04-24T18:15:00Z',
  },
];

export {
  Weeks,
  mockWeeks,
  AssignmentGroups,
  mockAssignmentGroups,
  Assignments,
  mockAssignments,
  AssignmentAttachments,
  mockAssignmentAttachments,
  AssignmentSubmissions,
  mockAssignmentSubmissions,
};


export interface Student {
  id: number;
  full_name: string;
  email: string;
  group: string;
  role: string,
  avatar_url?: string;
}

// Student Profile API Response Interfaces
export interface User {
  id: number;
  name: string;
  surname: string;
  email: string;
  role: string;
  created_at: string;
  updated_at: string;
}

export interface AcademicStats {
  degree_progress_percent: number;
  gpa: number;
  required_credits: number;
  total_credits_attempted: number;
  total_credits_earned: number;
}

export interface Course {
  id: number;
  title: string;
  description: string;
  created_at?: string;
  updated_at?: string;
}

export interface Degree {
  id: number;
  name: string;
  description: string;
  level: string;
  min_gpa: number;
  required_credits: number;
}

export interface StudentDegree {
  id: number;
  degree_id: number;
  start_date: string;
  expected_graduation_date: string;
  final_gpa: number;
  status: string;
  degree: Degree;
}

export interface EnrolledThread {
  id: number;
  title: string;
  max_students: number;
  course: Course;
}

export interface ProfileSummary {
  total_available_courses: number;
  total_degrees: number;
  total_enrolled_threads: number;
}

export interface StudentProfile {
  student_id: number;
  user: User;
  academic_stats: AcademicStats;
  degrees: StudentDegree[];
  enrolled_threads: EnrolledThread[];
  available_courses: Course[];
  summary: ProfileSummary;
}

export const MOCK_STUDENTS: Student[] = [
  {
    id: 1,
    full_name: 'Алексей Иванов',
    email: '<EMAIL>',
    group: 'Группа A',
    role: 'Student',
    avatar_url: 'https://i.pravatar.cc/150?img=1'
  },
  {
    id: 2,
    full_name: 'Мария Петрова',
    email: '<EMAIL>',
    group: 'Группа B',
    role: 'Student',
    avatar_url: 'https://i.pravatar.cc/150?img=2'
  },
  {
    id: 3,
    full_name: 'Игорь Сидоров',
    email: '<EMAIL>',
    group: 'Группа A',
    role: 'Student',
    avatar_url: 'https://i.pravatar.cc/150?img=3'
  },
  {
    id: 4,
    full_name: 'Светлана Смирнова',
    email: '<EMAIL>',
    group: 'Группа C',
    role: 'Student',
    avatar_url: 'https://i.pravatar.cc/150?img=4'
  }
];

// Mock documents data
export const DOCUMENTS_DATA = [
  {
    id: 1,
    name: 'Учебный план 2024-2025',
    description: 'Официальный учебный план на текущий академический год',
    category: 'academic',
    date: '15.01.2024',
    size: 2456000, // 2.45 MB
    icon: 'pdf.svg',
    url: '/assets/docs/example.pdf',
    filename: 'curriculum_2024.pdf'
  },
  {
    id: 2,
    name: 'Руководство для студентов',
    description: 'Информация о правилах и процедурах университета',
    category: 'guidelines',
    date: '10.02.2024',
    size: 1843000, // 1.84 MB
    icon: 'pdf.svg',
    url: '/assets/docs/example.pdf',
    filename: 'student_handbook.pdf'
  },
  {
    id: 3,
    name: 'Заявление на академический отпуск',
    description: 'Форма для оформления академического отпуска',
    category: 'forms',
    date: '05.03.2024',
    size: 245000, // 245 KB
    icon: 'doc.svg',
    url: '/assets/docs/example.pdf',
    filename: 'academic_leave_form.docx'
  },
  {
    id: 4,
    name: 'Расписание экзаменов - Весна 2024',
    description: 'Расписание экзаменов на весенний семестр',
    category: 'academic',
    date: '20.03.2024',
    size: 356000, // 356 KB
    icon: 'xls.svg',
    url: '/assets/docs/example.pdf',
    filename: 'exam_schedule_spring_2024.xlsx'
  },
  {
    id: 5,
    name: 'Правила проживания в общежитии',
    description: 'Правила и нормы для студентов, проживающих в общежитии',
    category: 'administrative',
    date: '12.01.2024',
    size: 567000, // 567 KB
    icon: 'pdf.svg',
    url: '/assets/docs/example.pdf',
    filename: 'dormitory_rules.pdf'
  },
  {
    id: 6,
    name: 'Заявление на перевод',
    description: 'Форма для перевода на другую специальность или факультет',
    category: 'forms',
    date: '15.02.2024',
    size: 198000, // 198 KB
    icon: 'doc.svg',
    url: '/assets/docs/example.pdf',
    filename: 'transfer_form.docx'
  },
  {
    id: 7,
    name: 'Календарь академических событий',
    description: 'Календарь важных дат и событий на учебный год',
    category: 'academic',
    date: '05.01.2024',
    size: 423000, // 423 KB
    icon: 'pdf.svg',
    url: '/assets/docs/example.pdf',
    filename: 'academic_calendar.pdf'
  },
  {
    id: 8,
    name: 'Руководство по написанию дипломной работы',
    description: 'Методические указания по подготовке и оформлению дипломной работы',
    category: 'guidelines',
    date: '25.02.2024',
    size: 3250000, // 3.25 MB
    icon: 'pdf.svg',
    url: '/assets/docs/example.pdf',
    filename: 'thesis_guidelines.pdf'
  },
  {
    id: 9,
    name: 'Заявление на материальную помощь',
    description: 'Форма для получения материальной помощи',
    category: 'forms',
    date: '10.03.2024',
    size: 215000, // 215 KB
    icon: 'doc.svg',
    url: '/assets/docs/example.pdf',
    filename: 'financial_aid_form.docx'
  },
  {
    id: 10,
    name: 'Положение о стипендиях',
    description: 'Информация о видах стипендий и условиях их получения',
    category: 'administrative',
    date: '18.01.2024',
    size: 876000, // 876 KB
    icon: 'pdf.svg',
    url: '/assets/docs/example.pdf',
    filename: 'scholarship_regulations.pdf'
  }
];
