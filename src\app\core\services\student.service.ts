import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environments } from '../../environments/environments';
import { StudentProfile } from '../../shared/mock-data';

@Injectable({
  providedIn: 'root'
})
export class StudentService {
  private apiUrl = environments.API;

  constructor(private http: HttpClient) {}

  /**
   * Get student profile information
   * @param studentId The ID of the student
   * @returns Observable with complete student profile data
   */
  getStudentProfile(studentId: number): Observable<StudentProfile> {
    return this.http.get<StudentProfile>(`${this.apiUrl}/students/${studentId}/profile`);
  }

  /**
   * Get available courses for a student
   * @param studentId The ID of the student
   * @returns Observable with available courses
   */
  getAvailableCourses(studentId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/students/${studentId}/available-courses`);
  }

  /**
   * Get available threads for a student
   * @param studentId The ID of the student
   * @returns Observable with available threads
   */
  getAvailableThreads(studentId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/students/${studentId}/available-threads`);
  }

  /**
   * Get student schedule
   * @param studentId The ID of the student
   * @returns Observable with student schedule
   */
  getStudentSchedule(studentId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/students/${studentId}/schedule`);
  }
}
