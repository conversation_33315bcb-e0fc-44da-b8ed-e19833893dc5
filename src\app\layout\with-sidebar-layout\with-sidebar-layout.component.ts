import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NotificationService, NotificationItem } from '../../core/services/notification.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-with-sidebar-layout',
  templateUrl: './with-sidebar-layout.component.html',
  styleUrls: ['./with-sidebar-layout.component.css']
})
export class WithSidebarLayoutComponent implements OnInit, OnDestroy {
  sidebarCollapsed = false;
  showNotifications = false;
  notifications: NotificationItem[] = [];
  private subscriptions: Subscription[] = [];

  constructor(private notificationService: NotificationService) {}

  ngOnInit(): void {
    // Subscribe to notifications
    const notificationsSub = this.notificationService.notifications$.subscribe(notifications => {
      this.notifications = notifications;
    });
    this.subscriptions.push(notificationsSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  toggleSidebar(): void {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }

  toggleNotifications(): void {
    this.showNotifications = !this.showNotifications;
  }

  formatTime(time: any): string {
    return this.notificationService.formatNotificationTime(time);
  }
}
