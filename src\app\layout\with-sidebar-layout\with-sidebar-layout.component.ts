import { Component, OnInit, OnDestroy } from '@angular/core';
import { NotificationService, NotificationItem } from '../../core/services/notification.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-with-sidebar-layout',
  templateUrl: './with-sidebar-layout.component.html',
  styleUrls: ['./with-sidebar-layout.component.css']
})
export class WithSidebarLayoutComponent implements OnInit, OnDestroy {
  sidebarCollapsed = false;
  showNotifications = false;
  isNotificationExpanded = false;
  notifications: NotificationItem[] = [];
  private subscriptions: Subscription[] = [];

  constructor(private notificationService: NotificationService) {}

  ngOnInit(): void {
    // Subscribe to notifications
    const notificationsSub = this.notificationService.notifications$.subscribe(notifications => {
      this.notifications = notifications;
    });
    this.subscriptions.push(notificationsSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  toggleSidebar(): void {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }

  toggleNotifications(): void {
    this.showNotifications = !this.showNotifications;

    // Если открываем панель уведомлений, помечаем как прочитанные
    if (this.showNotifications) {
      this.notificationService.markAsRead();
    }

    // Сбрасываем состояние раскрытия при закрытии панели
    if (!this.showNotifications) {
      this.isNotificationExpanded = false;
    }
  }

  toggleNotificationExpansion(): void {
    this.isNotificationExpanded = !this.isNotificationExpanded;
  }

  formatTime(time: any): string {
    return this.notificationService.formatNotificationTime(time);
  }
}
