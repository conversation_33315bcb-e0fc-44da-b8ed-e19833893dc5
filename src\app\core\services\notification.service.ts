import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, interval, switchMap, catchError, of } from 'rxjs';
import { environments } from '../../environments/environments';
import { AuthService } from './auth.service';
import { StorageService } from './storage.service';

export interface NotificationTime {
  seconds: number;
  nanos: number;
}

export interface Notification {
  id: number;
  title: string;
  message: string;
  priority: number;
  target_value: string;
  sender_id: number;
  send_email: boolean;
  email_subject: string;
  sent_at: NotificationTime;
  created_at: NotificationTime;
  updated_at: NotificationTime;
}

export interface NotificationRecipient {
  id: number;
  notification_id: number;
  user_id: number;
  created_at: NotificationTime;
  updated_at: NotificationTime;
}

export interface NotificationItem {
  notification: Notification;
  recipient: NotificationRecipient;
}

export interface NotificationResponse {
  notifications: NotificationItem[];
  total_count: number;
  unread_count: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = environments.API;
  private notificationsSubject = new BehaviorSubject<NotificationItem[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);
  private localUnreadCountSubject = new BehaviorSubject<number>(0);
  private totalCountSubject = new BehaviorSubject<number>(0);
  private previousTotalCount = 0;
  private pollingInterval: any;
  private startTime = Date.now();
  private lastReadTime = 0;

  public notifications$ = this.notificationsSubject.asObservable();
  public unreadCount$ = this.unreadCountSubject.asObservable();
  public localUnreadCount$ = this.localUnreadCountSubject.asObservable();
  public totalCount$ = this.totalCountSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private storageService: StorageService
  ) {
    // Initialize last read time to current time
    this.lastReadTime = Date.now();
  }

  /**
   * Start polling for notifications every 3 seconds
   */
  startPolling(): void {
    if (this.pollingInterval) {
      this.stopPolling();
    }

    // Check if user is logged in
    const user = this.storageService.getUser();
    const token = this.storageService.getToken();

    console.log('Starting notification polling...');
    console.log('User:', user);
    console.log('Token available:', !!token);
    console.log('API URL:', this.apiUrl);

    // Initial load
    this.loadNotifications().subscribe();

    // Poll every 3 seconds
    this.pollingInterval = interval(3000).pipe(
      switchMap(() => this.loadNotifications()),
      catchError(error => {
        console.error('Error polling notifications:', error);
        // Return empty response to prevent further errors
        return of(null);
      })
    ).subscribe(response => {
      // Additional safety check
      if (response === null) {
        console.warn('Received null response from polling');
      }
    });
  }

  /**
   * Stop polling for notifications
   */
  stopPolling(): void {
    if (this.pollingInterval) {
      this.pollingInterval.unsubscribe();
      this.pollingInterval = null;
    }
  }

  /**
   * Load notifications from API
   */
  private loadNotifications(): Observable<NotificationResponse | null> {
    // Get user from localStorage via StorageService
    const user = this.storageService.getUser();
    if (!user || !user.id) {
      console.warn('No user found in localStorage or user has no ID');
      return of(this.getMockNotifications());
    }

    console.log(`Loading notifications for user ID: ${user.id}`);

    // HTTP interceptor will automatically add Authorization header with Bearer token
    return this.http.get<NotificationResponse>(`${this.apiUrl}/users/${user.id}/notifications`).pipe(
      catchError(error => {
        console.error('Error loading notifications from API, using mock data:', error);
        console.error('API URL:', `${this.apiUrl}/users/${user.id}/notifications`);
        // Return mock data when API is not available
        return of(this.getMockNotifications());
      })
    ).pipe(
      switchMap(response => {
        if (response) {
          console.log('Successfully loaded notifications:', response);
          try {
            this.updateNotifications(response);
          } catch (error) {
            console.error('Error updating notifications:', error);
          }
        }
        return of(response);
      })
    );
  }

  /**
   * Get mock notifications for testing
   */
  private getMockNotifications(): NotificationResponse {
    const currentTime = Math.floor(Date.now() / 1000);
    const user = this.storageService.getUser();
    const userId = user?.id || 2; // fallback to user ID 2 if no user found

    console.log(`Generating mock notifications for user ID: ${userId}`);

    // Simulate new notifications appearing over time
    const baseNotifications: NotificationItem[] = [
      {
        notification: {
          id: 2,
          title: "Оценка выставлена",
          message: "Ваша работа оценена: 85/100",
          priority: 2,
          target_value: "Домашнее задание 2",
          sender_id: 1,
          send_email: false,
          email_subject: "",
          sent_at: { seconds: currentTime - 3600, nanos: 0 }, // 1 hour ago
          created_at: { seconds: currentTime - 3600, nanos: 0 },
          updated_at: { seconds: currentTime - 3600, nanos: 0 }
        },
        recipient: {
          id: 2,
          notification_id: 2,
          user_id: userId,
          created_at: { seconds: currentTime - 3600, nanos: 0 },
          updated_at: { seconds: currentTime - 3600, nanos: 0 }
        }
      }
    ];

    // Add new notification every 15 seconds for demo
    const timeSinceStart = Math.floor((Date.now() - this.startTime) / 15000);
    const newNotifications: NotificationItem[] = [];

    for (let i = 0; i < timeSinceStart; i++) {
      const notificationId = 100 + i;
      newNotifications.push({
        notification: {
          id: notificationId,
          title: `Новое уведомление ${i + 1}`,
          message: `Это тестовое уведомление номер ${i + 1}`,
          priority: 1,
          target_value: `Тест ${i + 1}`,
          sender_id: 1,
          send_email: true,
          email_subject: `Тестовое уведомление ${i + 1}`,
          sent_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 },
          created_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 },
          updated_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 }
        },
        recipient: {
          id: notificationId,
          notification_id: notificationId,
          user_id: userId,
          created_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 },
          updated_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 }
        }
      });
    }

    const allNotifications = [...newNotifications, ...baseNotifications];

    const mockResponse: NotificationResponse = {
      notifications: allNotifications || [],
      total_count: allNotifications ? allNotifications.length : 0,
      unread_count: newNotifications ? newNotifications.length + 1 : 1
    };

    console.log('Generated mock response:', mockResponse);
    return mockResponse;
  }

  /**
   * Update notifications and handle new notifications
   */
  private updateNotifications(response: NotificationResponse): void {
    // Validate response
    if (!response || !response.notifications) {
      console.warn('Invalid response received:', response);
      return;
    }

    const currentNotifications = this.notificationsSubject.value || [];
    const newNotifications = response.notifications || [];

    // Check if there are new notifications
    if (response.total_count > this.previousTotalCount) {
      // Calculate how many new notifications we have
      const newCount = response.total_count - this.previousTotalCount;

      // Get the new notifications (first newCount items)
      const newItems = newNotifications.slice(0, newCount);

      // Combine new notifications with existing ones, new ones first
      const updatedNotifications = [...newItems, ...currentNotifications];

      this.notificationsSubject.next(updatedNotifications);

      // Update local unread count only with new notifications after last read
      this.updateLocalUnreadCount(newNotifications);
    } else {
      // No new notifications, just update the list
      this.notificationsSubject.next(newNotifications);
      this.updateLocalUnreadCount(newNotifications);
    }

    this.unreadCountSubject.next(response.unread_count || 0);
    this.totalCountSubject.next(response.total_count || 0);
    this.previousTotalCount = response.total_count || 0;
  }

  /**
   * Update local unread count based on notifications newer than last read time
   */
  private updateLocalUnreadCount(notifications: NotificationItem[]): void {
    // Validate notifications array
    if (!notifications || !Array.isArray(notifications)) {
      console.warn('Invalid notifications array:', notifications);
      this.localUnreadCountSubject.next(0);
      return;
    }

    try {
      const unreadCount = notifications.filter(item => {
        // Validate item structure
        if (!item || !item.notification || !item.notification.sent_at) {
          console.warn('Invalid notification item:', item);
          return false;
        }

        // Check if notification is newer than last read time
        const sentTime = item.notification.sent_at.seconds * 1000;
        return sentTime > this.lastReadTime;
      }).length;

      this.localUnreadCountSubject.next(unreadCount);
    } catch (error) {
      console.error('Error updating local unread count:', error);
      this.localUnreadCountSubject.next(0);
    }
  }

  /**
   * Get current unread count
   */
  getUnreadCount(): number {
    return this.unreadCountSubject.value;
  }

  /**
   * Get current notifications
   */
  getNotifications(): NotificationItem[] {
    return this.notificationsSubject.value;
  }

  /**
   * Mark notifications as read (reset local unread count)
   */
  markAsRead(): void {
    this.lastReadTime = Date.now();
    this.localUnreadCountSubject.next(0);
    console.log('Notifications marked as read');
  }

  /**
   * Format notification time for display
   */
  formatNotificationTime(time: NotificationTime): string {
    const date = new Date(time.seconds * 1000);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) {
      return 'Только что';
    } else if (diffMinutes < 60) {
      return `${diffMinutes} мин назад`;
    } else if (diffHours < 24) {
      return `${diffHours} ч назад`;
    } else {
      return `${diffDays} дн назад`;
    }
  }
}
