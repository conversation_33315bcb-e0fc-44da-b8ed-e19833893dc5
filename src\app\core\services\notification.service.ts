import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, interval, switchMap, catchError, of } from 'rxjs';
import { environments } from '../../environments/environments';
import { AuthService } from './auth.service';

export interface NotificationTime {
  seconds: number;
  nanos: number;
}

export interface Notification {
  id: number;
  title: string;
  message: string;
  priority: number;
  target_value: string;
  sender_id: number;
  send_email: boolean;
  email_subject: string;
  sent_at: NotificationTime;
  created_at: NotificationTime;
  updated_at: NotificationTime;
}

export interface NotificationRecipient {
  id: number;
  notification_id: number;
  user_id: number;
  created_at: NotificationTime;
  updated_at: NotificationTime;
}

export interface NotificationItem {
  notification: Notification;
  recipient: NotificationRecipient;
}

export interface NotificationResponse {
  notifications: NotificationItem[];
  total_count: number;
  unread_count: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = environments.API;
  private notificationsSubject = new BehaviorSubject<NotificationItem[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);
  private totalCountSubject = new BehaviorSubject<number>(0);
  private previousTotalCount = 0;
  private pollingInterval: any;
  private startTime = Date.now();

  public notifications$ = this.notificationsSubject.asObservable();
  public unreadCount$ = this.unreadCountSubject.asObservable();
  public totalCount$ = this.totalCountSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Start polling for notifications every 3 seconds
   */
  startPolling(): void {
    if (this.pollingInterval) {
      this.stopPolling();
    }

    // Initial load
    this.loadNotifications().subscribe();

    // Poll every 3 seconds
    this.pollingInterval = interval(3000).pipe(
      switchMap(() => this.loadNotifications()),
      catchError(error => {
        console.error('Error polling notifications:', error);
        return of(null);
      })
    ).subscribe();
  }

  /**
   * Stop polling for notifications
   */
  stopPolling(): void {
    if (this.pollingInterval) {
      this.pollingInterval.unsubscribe();
      this.pollingInterval = null;
    }
  }

  /**
   * Load notifications from API
   */
  private loadNotifications(): Observable<NotificationResponse | null> {
    const user = this.authService.getCurrentUser();
    if (!user || !user.id) {
      return of(null);
    }

    return this.http.get<NotificationResponse>(`${this.apiUrl}/users/${user.id}/notifications`).pipe(
      catchError(error => {
        console.error('Error loading notifications, using mock data:', error);
        // Return mock data when API is not available
        return of(this.getMockNotifications());
      })
    ).pipe(
      switchMap(response => {
        if (response) {
          this.updateNotifications(response);
        }
        return of(response);
      })
    );
  }

  /**
   * Get mock notifications for testing
   */
  private getMockNotifications(): NotificationResponse {
    const currentTime = Math.floor(Date.now() / 1000);

    // Simulate new notifications appearing over time
    const baseNotifications: NotificationItem[] = [
      {
        notification: {
          id: 2,
          title: "Оценка выставлена",
          message: "Ваша работа оценена: 85/100",
          priority: 2,
          target_value: "Домашнее задание 2",
          sender_id: 1,
          send_email: false,
          email_subject: "",
          sent_at: { seconds: currentTime - 3600, nanos: 0 }, // 1 hour ago
          created_at: { seconds: currentTime - 3600, nanos: 0 },
          updated_at: { seconds: currentTime - 3600, nanos: 0 }
        },
        recipient: {
          id: 2,
          notification_id: 2,
          user_id: 2,
          created_at: { seconds: currentTime - 3600, nanos: 0 },
          updated_at: { seconds: currentTime - 3600, nanos: 0 }
        }
      }
    ];

    // Add new notification every 15 seconds for demo
    const timeSinceStart = Math.floor((Date.now() - this.startTime) / 15000);
    const newNotifications: NotificationItem[] = [];

    for (let i = 0; i < timeSinceStart; i++) {
      const notificationId = 100 + i;
      newNotifications.push({
        notification: {
          id: notificationId,
          title: `Новое уведомление ${i + 1}`,
          message: `Это тестовое уведомление номер ${i + 1}`,
          priority: 1,
          target_value: `Тест ${i + 1}`,
          sender_id: 1,
          send_email: true,
          email_subject: `Тестовое уведомление ${i + 1}`,
          sent_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 },
          created_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 },
          updated_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 }
        },
        recipient: {
          id: notificationId,
          notification_id: notificationId,
          user_id: 2,
          created_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 },
          updated_at: { seconds: currentTime - (timeSinceStart - i) * 15, nanos: 0 }
        }
      });
    }

    const allNotifications = [...newNotifications, ...baseNotifications];

    return {
      notifications: allNotifications,
      total_count: allNotifications.length,
      unread_count: newNotifications.length + 1
    };
  }

  /**
   * Update notifications and handle new notifications
   */
  private updateNotifications(response: NotificationResponse): void {
    const currentNotifications = this.notificationsSubject.value;
    const newNotifications = response.notifications;
    
    // Check if there are new notifications
    if (response.total_count > this.previousTotalCount) {
      // Calculate how many new notifications we have
      const newCount = response.total_count - this.previousTotalCount;
      
      // Get the new notifications (first newCount items)
      const newItems = newNotifications.slice(0, newCount);
      
      // Combine new notifications with existing ones, new ones first
      const updatedNotifications = [...newItems, ...currentNotifications];
      
      this.notificationsSubject.next(updatedNotifications);
    } else {
      // No new notifications, just update the list
      this.notificationsSubject.next(newNotifications);
    }

    this.unreadCountSubject.next(response.unread_count);
    this.totalCountSubject.next(response.total_count);
    this.previousTotalCount = response.total_count;
  }

  /**
   * Get current unread count
   */
  getUnreadCount(): number {
    return this.unreadCountSubject.value;
  }

  /**
   * Get current notifications
   */
  getNotifications(): NotificationItem[] {
    return this.notificationsSubject.value;
  }

  /**
   * Format notification time for display
   */
  formatNotificationTime(time: NotificationTime): string {
    const date = new Date(time.seconds * 1000);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) {
      return 'Только что';
    } else if (diffMinutes < 60) {
      return `${diffMinutes} мин назад`;
    } else if (diffHours < 24) {
      return `${diffHours} ч назад`;
    } else {
      return `${diffDays} дн назад`;
    }
  }
}
