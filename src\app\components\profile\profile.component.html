<app-breadcrumbs [items]="breadcrumbs"/>

<!-- Состояние загрузки -->
<div *ngIf="loading" class="max-w-screen-xl mb-5">
  <div class="flex justify-center items-center py-20">
    <mat-spinner diameter="50"></mat-spinner>
  </div>
</div>

<!-- Состояние ошибки -->
<div *ngIf="error && !loading" class="max-w-screen-xl mb-5">
  <div class="bg-red-50 border border-red-200 rounded-lg p-4">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
      </svg>
      <span class="text-red-800">{{ error }}</span>
    </div>
  </div>
</div>

<!-- Содержимое профиля -->
<div *ngIf="!loading && !error && user" class="max-w-screen-xl mb-5">
  <div class="max-w-6xl">

    <!-- Заголовок пользователя -->
    <div class="flex justify-between gap-2 mb-4 border p-4 rounded-xl">
      <div class="flex items-center gap-2">
        <div class="rounded-full size-20">
          <img src="/assets/icons/profile.png" class="w-full rounded-full" alt="">
        </div>
        <div>
          <div class="text-lg font-semibold text-blue-500 capitalize">
            {{ user.name }} {{ user.surname }}
          </div>
          <div class="text-sm text-gray-500 font-medium flex gap-1">
            <span *ngIf="user.role === 'student'">Студент</span>
            <span *ngIf="user.role === 'teacher'">Преподаватель</span>
            <span *ngIf="user.role === 'admin'">Администратор</span>
          </div>
          <div class="text-xs font-normal">Алматы, Нархоз</div>
        </div>
      </div>
      <div class="flex items-start" *ngIf="user.role === 'student'">
        <button mat-icon-button
                (click)="refreshProfile()"
                [disabled]="loading"
                matTooltip="Обновить профиль"
                class="text-gray-600 hover:text-blue-600">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Академическая статистика (только для студентов) -->
    <div *ngIf="user.role === 'student' && studentProfile" class="mb-4 border p-4 py-6 rounded-xl">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Академическая статистика
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- GPA -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-xs text-gray-600 mb-1">Текущий GPA</div>
          <div class="text-2xl font-bold" [ngClass]="getGpaColor(studentProfile.academic_stats.gpa)">
            {{ studentProfile.academic_stats.gpa | number:'1.2-2' }}
          </div>
        </div>

        <!-- Degree Progress -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-xs text-gray-600 mb-1">Прогресс обучения</div>
          <div class="text-2xl font-bold text-blue-600">
            {{ studentProfile.academic_stats.degree_progress_percent | number:'1.1-1' }}%
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div class="h-2 rounded-full"
                 [ngClass]="getProgressColor(studentProfile.academic_stats.degree_progress_percent)"
                 [style.width.%]="studentProfile.academic_stats.degree_progress_percent">
            </div>
          </div>
        </div>

        <!-- Credits Earned -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-xs text-gray-600 mb-1">Заработанные кредиты</div>
          <div class="text-2xl font-bold text-green-600">
            {{ studentProfile.academic_stats.total_credits_earned }}
          </div>
          <div class="text-xs text-gray-500">
            из {{ studentProfile.academic_stats.required_credits }} требуемых
          </div>
        </div>

        <!-- Credits Attempted -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-xs text-gray-600 mb-1">Попытки кредитов</div>
          <div class="text-2xl font-bold text-purple-600">
            {{ studentProfile.academic_stats.total_credits_attempted }}
          </div>
        </div>
      </div>
    </div>

    <!-- Персональная информация -->
    <div class="mb-4 border p-4 py-6 rounded-xl">
      <div class="flex justify-between">
        <div class="w-9/12">
          <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
            <img src="/assets/icons/info.svg" width="20" class="mb-0.5" alt="">
            Персональная информация
          </div>
          <div class="mt-2 gap-y-5 grid md:grid-cols-2 grid-cols-1 text-xs text-gray-600 font-medium">
            <div>
              <div class="mb-1">Имя:</div>
              <div class="text-gray-700">{{ user.name }}</div>
            </div>
            <div>
              <div class="mb-1">Фамилия:</div>
              <div class="text-gray-700">{{ user.surname }}</div>
            </div>
            <div>
              <div class="mb-1">Email адрес:</div>
              <div class="text-gray-700">{{ user.email }}</div>
            </div>
            <div *ngIf="user.role === 'student' && studentProfile">
              <div class="mb-1">ID студента:</div>
              <div class="text-gray-700">{{ studentProfile.student_id }}</div>
            </div>
            <div *ngIf="user.role !== 'student'">
              <div class="mb-1">ID пользователя:</div>
              <div class="text-gray-700">{{ user.id }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Программы обучения (только для студентов) -->
    <div *ngIf="user.role === 'student' && studentProfile && studentProfile.degrees && studentProfile.degrees.length > 0"
         class="mb-4 border p-4 py-6 rounded-xl">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
        </svg>
        Программы обучения
      </div>

      <div class="space-y-4">
        <div *ngFor="let studentDegree of studentProfile.degrees" class="bg-gray-50 p-4 rounded-lg">
          <div class="flex justify-between items-start mb-3">
            <div>
              <h3 class="font-semibold text-gray-800">{{ studentDegree.degree.name }}</h3>
              <p class="text-sm text-gray-600">{{ studentDegree.degree.description }}</p>
            </div>
            <span class="px-2 py-1 text-xs rounded-full font-medium"
                  [ngClass]="getDegreeStatusColor(studentDegree.status)">
              <span *ngIf="studentDegree.status === 'IN_PROGRESS'">В процессе</span>
              <span *ngIf="studentDegree.status === 'COMPLETED'">Завершено</span>
              <span *ngIf="studentDegree.status === 'SUSPENDED'">Приостановлено</span>
            </span>
          </div>

          <div class="grid md:grid-cols-3 gap-4 text-xs">
            <div>
              <div class="text-gray-600 mb-1">Уровень:</div>
              <div class="font-medium">{{ studentDegree.degree.level }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Дата начала:</div>
              <div class="font-medium">{{ formatDate(studentDegree.start_date) }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Ожидаемый выпуск:</div>
              <div class="font-medium">{{ formatDate(studentDegree.expected_graduation_date) }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Требуемые кредиты:</div>
              <div class="font-medium">{{ studentDegree.degree.required_credits }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Минимальный GPA:</div>
              <div class="font-medium">{{ studentDegree.degree.min_gpa }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Итоговый GPA:</div>
              <div class="font-medium">{{ studentDegree.final_gpa || 'В процессе' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Записанные курсы (только для студентов) -->
    <div *ngIf="user.role === 'student' && studentProfile && studentProfile.enrolled_threads && studentProfile.enrolled_threads.length > 0"
         class="mb-4 border p-4 py-6 rounded-xl">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Записанные курсы
      </div>

      <div class="grid md:grid-cols-2 gap-4">
        <div *ngFor="let thread of studentProfile.enrolled_threads" class="bg-gray-50 p-4 rounded-lg">
          <div class="flex justify-between items-start mb-3">
            <div>
              <h3 class="font-semibold text-gray-800">{{ thread.course.title }}</h3>
              <p class="text-sm text-gray-600 mb-2">{{ thread.title }}</p>
              <p class="text-xs text-gray-500">{{ truncateDescription(thread.course.description, 100) }}</p>
            </div>
          </div>

          <div class="flex justify-between items-center mt-3 pt-3 border-t border-gray-200">
            <div class="text-xs text-gray-600">
              <span class="font-medium">Макс. студентов:</span> {{ thread.max_students }}
            </div>
            <button class="text-xs text-blue-600 hover:text-blue-800 font-medium"
                    [routerLink]="['/thread', thread.id]">
              Подробнее →
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Доступные курсы (только для студентов) -->
    <div *ngIf="user.role === 'student' && studentProfile && studentProfile.available_courses && studentProfile.available_courses.length > 0"
         class="mb-4 border p-4 py-6 rounded-xl">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        Доступные курсы
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div *ngFor="let course of studentProfile.available_courses" class="bg-gray-50 p-4 rounded-lg">
          <div class="mb-3">
            <h3 class="font-semibold text-gray-800 mb-2">{{ course.title }}</h3>
            <p class="text-xs text-gray-500">{{ truncateDescription(course.description, 80) }}</p>
          </div>

          <div class="flex justify-between items-center mt-3 pt-3 border-t border-gray-200">
            <div class="text-xs text-gray-600">
              <span class="font-medium">ID курса:</span> {{ course.id }}
            </div>
            <button class="text-xs text-green-600 hover:text-green-800 font-medium"
                    [routerLink]="['/register']">
              Записаться →
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Сводная статистика (только для студентов) -->
    <div *ngIf="user.role === 'student' && studentProfile" class="mb-4 border p-4 py-6 rounded-xl">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
        </svg>
        Сводка
      </div>

      <div class="grid md:grid-cols-3 gap-4">
        <div class="bg-blue-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-blue-600">{{ studentProfile.summary.total_enrolled_threads }}</div>
          <div class="text-sm text-blue-800">Записанные курсы</div>
        </div>

        <div class="bg-green-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-green-600">{{ studentProfile.summary.total_available_courses }}</div>
          <div class="text-sm text-green-800">Доступные курсы</div>
        </div>

        <div class="bg-purple-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-purple-600">{{ studentProfile.summary.total_degrees }}</div>
          <div class="text-sm text-purple-800">Программы обучения</div>
        </div>
      </div>
    </div>

    <!-- Информация для преподавателей -->
    <div *ngIf="user.role === 'teacher'" class="mb-4 border p-4 py-6 rounded-xl">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"></path>
        </svg>
        Информация преподавателя
      </div>

      <div class="bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600 mb-4">
          Добро пожаловать в систему управления обучением! Как преподаватель, вы можете:
        </p>
        <ul class="text-sm text-gray-700 space-y-2">
          <li class="flex items-center">
            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Управлять курсами и заданиями
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Оценивать работы студентов
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Отслеживать посещаемость
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Просматривать расписание
          </li>
        </ul>
      </div>
    </div>

  </div>
</div>