# Система уведомлений

## Реализованная функциональность

### 1. Endpoint `/users/2/notifications`
- Сервис автоматически отправляет запросы к API endpoint `http://localhost:8081/users/{user_id}/notifications`
- При недоступности API используются mock данные для демонстрации
- Ожидаемый формат ответа:
```json
{
    "notifications": [
        {
            "notification": {
                "id": 1,
                "title": "Test",
                "message": "Test",
                "priority": 1,
                "target_value": "TEST TARGET VALUE",
                "sender_id": 1,
                "send_email": true,
                "email_subject": "TEST EMAIL SUBJECT",
                "sent_at": {
                    "seconds": 1749753981,
                    "nanos": 445873000
                },
                "created_at": {
                    "seconds": 1749753976,
                    "nanos": 928960000
                },
                "updated_at": {
                    "seconds": 1749753981,
                    "nanos": 445873000
                }
            },
            "recipient": {
                "id": 2,
                "notification_id": 1,
                "user_id": 2,
                "created_at": {
                    "seconds": 1749753976,
                    "nanos": 949109000
                },
                "updated_at": {
                    "seconds": 1749753976,
                    "nanos": 949109000
                }
            }
        }
    ],
    "total_count": 1,
    "unread_count": 1
}
```

### 2. Автоматическое обновление каждые 3 секунды
- Сервис `NotificationService` автоматически опрашивает API каждые 3 секунды
- Polling запускается при инициализации header компонента
- Останавливается при уничтожении компонента

### 3. Отображение новых уведомлений сверху
- Новые уведомления определяются по изменению `total_count`
- Новые уведомления добавляются в начало списка
- Реализована логика сравнения предыдущего и текущего количества уведомлений

### 4. Минималистичный дизайн
- Локальный счетчик непрочитанных уведомлений в header (красный кружок)
- Счетчик обнуляется при клике на иконку уведомлений (помечает как прочитанные)
- Боковая панель с уведомлениями
- Цветовая индикация по приоритету:
  - Приоритет 1: синий цвет
  - Приоритет 2: желтый цвет
  - Приоритет 3: красный цвет
- Адаптивный дизайн для мобильных устройств
- Исправлена ошибка с undefined length при отсутствии уведомлений

## Файлы, которые были изменены/созданы

### Новые файлы:
1. `src/app/core/services/notification.service.ts` - основной сервис для работы с уведомлениями

### Измененные файлы:
1. `src/app/components/header/header.component.html` - добавлен счетчик уведомлений
2. `src/app/components/header/header.component.ts` - интеграция с сервисом уведомлений
3. `src/app/layout/with-sidebar-layout/with-sidebar-layout.component.html` - панель уведомлений
4. `src/app/layout/with-sidebar-layout/with-sidebar-layout.component.ts` - логика отображения

## Как использовать

### 1. Запуск приложения
```bash
npm start
```

### 2. Тестирование
- Откройте приложение в браузере
- Войдите в систему (если требуется аутентификация)
- Нажмите на иконку колокольчика в header для открытия панели уведомлений
- Наблюдайте за автоматическим обновлением каждые 3 секунды
- При клике на иконку уведомлений счетчик обнуляется (помечает как прочитанные)
- Новые уведомления будут увеличивать счетчик

### 3. Mock данные
При недоступности backend API используются mock данные:
- Базовое уведомление "Оценка выставлена"
- Новые тестовые уведомления появляются каждые 15 секунд для демонстрации

## Особенности реализации

### 1. Аутентификация и авторизация
- User ID автоматически получается из localStorage через StorageService
- Access token автоматически добавляется к запросам через HTTP interceptor
- Поддержка автоматического обновления токенов при истечении

### 2. Локальный счетчик уведомлений
- Ведется отдельный локальный счетчик непрочитанных уведомлений
- Счетчик обнуляется при открытии панели уведомлений
- Учитывает только уведомления, полученные после последнего "прочтения"

### 3. Управление подписками
- Все RxJS подписки корректно отписываются при уничтожении компонентов
- Polling останавливается при выходе из приложения

### 4. Обработка ошибок
- При ошибках API автоматически переключается на mock данные
- Логирование ошибок в консоль
- Защита от ошибок undefined при отсутствии данных

### 5. Производительность
- Использование BehaviorSubject для кэширования данных
- Минимальные перерисовки интерфейса
- Оптимизированные запросы к API
- Убрана анимация мигания для улучшения UX

### 6. Типизация
- Полная типизация TypeScript для всех интерфейсов
- Строгая типизация API ответов

## Настройка для production

### 1. API URL
Измените URL в `src/app/environments/environments.ts`:
```typescript
export const environments = {
  production: true,
  API: 'https://your-api-domain.com',
}
```

### 2. Отключение mock данных
Удалите fallback на mock данные в `NotificationService.loadNotifications()` для production версии.

### 3. Настройка интервала polling
Измените интервал в `NotificationService.startPolling()` если требуется:
```typescript
// Изменить с 3000 на нужное значение в миллисекундах
this.pollingInterval = interval(3000)
```
