import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { NotificationService, NotificationResponse } from './notification.service';
import { AuthService } from './auth.service';

describe('NotificationService', () => {
  let service: NotificationService;
  let httpMock: HttpTestingController;
  let authServiceSpy: jasmine.SpyObj<AuthService>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        NotificationService,
        { provide: AuthService, useValue: spy }
      ]
    });

    service = TestBed.inject(NotificationService);
    httpMock = TestBed.inject(HttpTestingController);
    authServiceSpy = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
  });

  afterEach(() => {
    httpMock.verify();
    service.stopPolling();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get unread count', () => {
    expect(service.getUnreadCount()).toBe(0);
  });

  it('should get notifications', () => {
    expect(service.getNotifications()).toEqual([]);
  });

  it('should format notification time correctly', () => {
    const time = {
      seconds: Math.floor(Date.now() / 1000) - 300, // 5 minutes ago
      nanos: 0
    };
    
    const formatted = service.formatNotificationTime(time);
    expect(formatted).toContain('мин назад');
  });

  it('should handle API error and use mock data', () => {
    authServiceSpy.getCurrentUser.and.returnValue({ id: 2, name: 'Test User' });

    service.startPolling();

    const req = httpMock.expectOne('http://localhost:8081/users/2/notifications');
    expect(req.request.method).toBe('GET');

    // Simulate API error
    req.error(new ErrorEvent('Network error'));

    // Should fall back to mock data
    service.notifications$.subscribe(notifications => {
      expect(notifications.length).toBeGreaterThan(0);
    });

    service.unreadCount$.subscribe(count => {
      expect(count).toBeGreaterThan(0);
    });
  });

  it('should handle successful API response', () => {
    authServiceSpy.getCurrentUser.and.returnValue({ id: 2, name: 'Test User' });

    const mockResponse: NotificationResponse = {
      notifications: [
        {
          notification: {
            id: 1,
            title: 'Test Notification',
            message: 'Test Message',
            priority: 1,
            target_value: 'Test Target',
            sender_id: 1,
            send_email: true,
            email_subject: 'Test Subject',
            sent_at: { seconds: Math.floor(Date.now() / 1000), nanos: 0 },
            created_at: { seconds: Math.floor(Date.now() / 1000), nanos: 0 },
            updated_at: { seconds: Math.floor(Date.now() / 1000), nanos: 0 }
          },
          recipient: {
            id: 1,
            notification_id: 1,
            user_id: 2,
            created_at: { seconds: Math.floor(Date.now() / 1000), nanos: 0 },
            updated_at: { seconds: Math.floor(Date.now() / 1000), nanos: 0 }
          }
        }
      ],
      total_count: 1,
      unread_count: 1
    };

    service.startPolling();

    const req = httpMock.expectOne('http://localhost:8081/users/2/notifications');
    req.flush(mockResponse);

    service.notifications$.subscribe(notifications => {
      expect(notifications.length).toBe(1);
      expect(notifications[0].notification.title).toBe('Test Notification');
    });

    service.unreadCount$.subscribe(count => {
      expect(count).toBe(1);
    });
  });
});
