<div class="relative">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div class="text-lg font-medium text-gray-800 dark:text-gray-200">
      Итоговые оценки
    </div>
    <div *ngIf="!isTeacher" class="text-sm text-gray-500 dark:text-gray-400">
      Только для просмотра
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div class="spinner rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
  </div>

  <!-- No students message -->
  <div *ngIf="!isLoading && students.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
    <p>В этой группе нет студентов</p>
  </div>

  <!-- Grades table -->
  <div *ngIf="!isLoading && students.length > 0" class="overflow-x-auto rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
      <thead class="bg-gray-50 dark:bg-gray-800">
        <tr>
          <!-- Student column -->
          <th scope="col" class="sticky left-0 bg-gray-50 dark:bg-gray-800 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-r border-gray-200 dark:border-gray-700">
            Студент
          </th>

          <!-- Assignment columns -->
          <th *ngFor="let assignment of assignments"
              scope="col"
              class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider min-w-[100px]"
              [title]="assignment.title">
            <div class="truncate max-w-[100px]">
              {{ assignment.title.length > 12 ? (assignment.title | slice:0:12) + '...' : assignment.title }}
            </div>
            <div class="text-xs text-gray-400 font-normal normal-case">
              Макс: {{ assignment.max_points || 100 }}
            </div>
          </th>

          <!-- Final grade column -->
          <th scope="col" class="sticky right-0 bg-gray-50 dark:bg-gray-800 px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-l border-gray-200 dark:border-gray-700">
            Итоговая оценка
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
        <tr *ngFor="let student of students" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          <!-- Student info -->
          <td class="sticky left-0 bg-white dark:bg-gray-800 px-4 py-3 whitespace-nowrap border-r border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
              <!-- Avatar with initials -->
              <div class="flex-shrink-0 h-8 w-8 rounded-full bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300 flex items-center justify-center text-sm font-medium">
                {{ getInitials(student) }}
              </div>
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ student.name }} {{ student.surname }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  ID: {{ student.id }}
                </div>
              </div>
            </div>
          </td>

          <!-- Assignment grades -->
          <td *ngFor="let assignment of assignments"
              class="px-3 py-3 whitespace-nowrap text-center text-sm text-gray-900 dark:text-white">
            <span class="inline-flex items-center justify-center w-12 h-8 rounded-md text-xs font-medium"
                  [ngClass]="{
                    'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': isGradeExcellent(getAssignmentGrade(student.id, assignment.id)),
                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': isGradeGood(getAssignmentGrade(student.id, assignment.id)),
                    'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': isGradePoor(getAssignmentGrade(student.id, assignment.id)),
                    'bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-400': getAssignmentGrade(student.id, assignment.id) === '-'
                  }">
              {{ getAssignmentGrade(student.id, assignment.id) }}
            </span>
          </td>

          <!-- Final grade -->
          <td class="sticky right-0 bg-white dark:bg-gray-800 px-4 py-3 whitespace-nowrap text-center border-l border-gray-200 dark:border-gray-700">
            <div *ngIf="!editingGrades[student.id]" class="flex items-center justify-center space-x-2">
              <span class="inline-flex items-center justify-center w-16 h-8 rounded-md text-sm font-medium"
                    [ngClass]="{
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200': getFinalGrade(student.id) >= 70,
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200': getFinalGrade(student.id) >= 50 && getFinalGrade(student.id) < 70,
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200': getFinalGrade(student.id) > 0 && getFinalGrade(student.id) < 50,
                      'bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-400': getFinalGrade(student.id) === 0
                    }">
                {{ getFinalGrade(student.id) || '-' }}
              </span>
              <button *ngIf="isTeacher"
                      (click)="startEditingGrade(student.id)"
                      class="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
            </div>

            <!-- Edit mode -->
            <div *ngIf="editingGrades[student.id]" class="flex flex-col items-center justify-center space-y-1">
              <div class="flex items-center space-x-2">
                <input type="number"
                       [min]="getMinFinalGrade(student.id)"
                       max="100"
                       [value]="tempGrades[student.id]"
                       (input)="onTempGradeChange(student.id, $event)"
                       class="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-center">
                <button (click)="saveFinalGrade(student.id)"
                        class="p-1 text-green-600 hover:text-green-700 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </button>
                <button (click)="cancelEditingGrade(student.id)"
                        class="p-1 text-red-600 hover:text-red-700 transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Мин: {{ getMinFinalGrade(student.id) }} | Макс: 100
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Legend and Info -->
  <div *ngIf="!isLoading && students.length > 0" class="mt-4 space-y-3">
    <!-- Grade Legend -->
    <div class="flex flex-wrap gap-4 text-xs">
      <div class="flex items-center space-x-2">
        <span class="inline-block w-4 h-4 bg-green-100 dark:bg-green-900 rounded"></span>
        <span class="text-gray-600 dark:text-gray-400">Отлично (70-100)</span>
      </div>
      <div class="flex items-center space-x-2">
        <span class="inline-block w-4 h-4 bg-yellow-100 dark:bg-yellow-900 rounded"></span>
        <span class="text-gray-600 dark:text-gray-400">Хорошо (50-69)</span>
      </div>
      <div class="flex items-center space-x-2">
        <span class="inline-block w-4 h-4 bg-red-100 dark:bg-red-900 rounded"></span>
        <span class="text-gray-600 dark:text-gray-400">Неудовлетворительно (0-49)</span>
      </div>
      <div class="flex items-center space-x-2">
        <span class="inline-block w-4 h-4 bg-gray-100 dark:bg-gray-700 rounded"></span>
        <span class="text-gray-600 dark:text-gray-400">Не оценено</span>
      </div>
    </div>

    <!-- Grading Rules Info -->
    <div *ngIf="isTeacher" class="text-xs text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
      <div class="font-medium text-blue-800 dark:text-blue-200 mb-1">Правила выставления итоговых оценок:</div>
      <ul class="space-y-1">
        <li>• Итоговая оценка не может быть меньше суммы баллов за задания</li>
        <li>• Максимальная итоговая оценка: 100 баллов</li>
        <li>• Сумма баллов автоматически рассчитывается из выполненных заданий</li>
      </ul>
    </div>
  </div>
</div>
