<div class="flex min-h-screen bg-secondary">
  <app-side-bar [collapsed]="sidebarCollapsed" (toggleSidebarEvent)="toggleSidebar()"></app-side-bar>

  <div class="flex-1 transition-all duration-300" [ngClass]="{'ml-20': sidebarCollapsed, 'ml-0 md:ml-64': !sidebarCollapsed}">
    <app-header
      (toggleSidebarEvent)="toggleSidebar()"
      (toggleNotificationsEvent)="toggleNotifications()">
    </app-header>

    <div class="flex relative">
      <main class="flex-1 px-3 sm:px-5 mt-2 w-full">
        <router-outlet></router-outlet>
      </main>

      <aside *ngIf="showNotifications" class="fixed right-0 top-16 border-l card overflow-auto z-20 md:relative md:top-0 transition-all duration-300"
             [ngClass]="{
               'w-72 h-96': !isNotificationExpanded,
               'w-80 h-[calc(100vh-64px)]': isNotificationExpanded
             }">
        <div class="p-4 text-xs">
          <div class="flex justify-between items-center mb-4">
            <div class="font-medium">
              {{ 'NOTIFICATIONS.TITLE' | translate }}
              <span *ngIf="notifications && notifications.length > 0" class="ml-2 text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded-full">
                {{ notifications.length }}
              </span>
            </div>
            <div class="flex items-center gap-2">
              <div class="text-xs text-gray-500">3 сек</div>
              <button
                (click)="toggleNotificationExpansion()"
                class="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
                [title]="isNotificationExpanded ? 'Свернуть' : 'Развернуть'">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform"
                     [ngClass]="{'rotate-180': isNotificationExpanded}"
                     viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M6 9l6 6 6-6"/>
                </svg>
              </button>
            </div>
          </div>

          <!-- Notifications list -->
          <div *ngIf="notifications && notifications.length > 0; else emptyState" class="space-y-2 overflow-y-auto"
               [ngClass]="{
                 'max-h-64': !isNotificationExpanded,
                 'max-h-96': isNotificationExpanded
               }">
            <div *ngFor="let item of notifications; let i = index"
                 class="p-2 rounded-lg border-l-4 transition-all duration-300 hover:shadow-sm cursor-pointer"
                 [ngClass]="{
                   'bg-blue-50 dark:bg-blue-900/20 border-l-blue-500': item.notification.priority === 1,
                   'bg-yellow-50 dark:bg-yellow-900/20 border-l-yellow-500': item.notification.priority === 2,
                   'bg-orange-50 dark:bg-orange-900/20 border-l-orange-500': item.notification.priority === 3,
                   'bg-red-50 dark:bg-red-900/20 border-l-red-500': item.notification.priority === 4,
                   'bg-purple-50 dark:bg-purple-900/20 border-l-purple-500': item.notification.priority === 5
                 }"
                 (click)="toggleNotificationItem(i)">
              <div class="flex justify-between items-start mb-1">
                <div class="flex items-center gap-2 flex-1 min-w-0">
                  <div class="w-2 h-2 rounded-full flex-shrink-0"
                       [ngClass]="{
                         'bg-blue-500': item.notification.priority === 1,
                         'bg-yellow-500': item.notification.priority === 2,
                         'bg-orange-500': item.notification.priority === 3,
                         'bg-red-500': item.notification.priority === 4,
                         'bg-purple-500': item.notification.priority === 5
                       }"></div>
                  <h4 class="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">{{ item.notification.title }}</h4>
                </div>
                <span class="text-xs text-gray-500 flex-shrink-0 ml-2">{{ formatTime(item.notification.sent_at) }}</span>
              </div>

              <!-- Сокращенное сообщение -->
              <p *ngIf="!isNotificationItemExpanded(i)"
                 class="text-xs text-gray-600 dark:text-gray-300 line-clamp-1">
                {{ item.notification.message }}
              </p>

              <!-- Полное сообщение -->
              <div *ngIf="isNotificationItemExpanded(i)" class="text-xs text-gray-600 dark:text-gray-300 mb-2">
                <p class="mb-2">{{ item.notification.message }}</p>
                <div *ngIf="item.notification.target_value" class="text-xs text-gray-500 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                  {{ item.notification.target_value }}
                </div>
              </div>
            </div>
          </div>

          <!-- Empty state - No notifications -->
          <ng-template #emptyState>
            <div class="py-8 text-center">
              <div class="bg-gray-100 dark:bg-gray-800 rounded-full p-3 mx-auto w-14 h-14 flex items-center justify-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                  <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                </svg>
              </div>
              <p class="text-sm font-medium text-gray-600 dark:text-gray-300">{{ 'NOTIFICATIONS.NO_NOTIFICATIONS' | translate }}</p>
              <p class="text-xs text-gray-500 mt-1">{{ 'NOTIFICATIONS.WILL_APPEAR_LATER' | translate }}</p>
            </div>
          </ng-template>
        </div>
      </aside>
    </div>
  </div>
</div>

