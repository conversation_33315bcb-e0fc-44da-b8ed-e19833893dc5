import {Component, OnInit} from '@angular/core';
import {StorageService} from "../../core/services/storage.service";
import {StudentService} from "../../core/services/student.service";
import {StudentProfile} from "../../shared/mock-data";
import {MatSnackBar} from "@angular/material/snack-bar";

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit{

  user: any;
  studentProfile: StudentProfile | null = null;
  loading = true;
  error: string | null = null;

  breadcrumbs: { label: string, url?: string }[] = [
    { label: 'Главная', url: '/' },
    { label: 'Профиль', },
  ];

  constructor(
    private storageService: StorageService,
    private studentService: StudentService,
    private snackBar: MatSnackBar
  ) {
  }

  ngOnInit() {
    this.user = this.storageService.getUser();
    console.log(this.user);

    if (this.user && this.user.id) {
      // Только для студентов загружаем полную информацию профиля
      if (this.user.role === 'student') {
        this.loadStudentProfile();
      } else {
        // Для учителей и других ролей просто показываем базовую информацию
        this.loading = false;
      }
    } else {
      this.loading = false;
      this.error = 'Информация о пользователе не найдена';
    }
  }

  private loadStudentProfile() {
    this.loading = true;
    this.error = null;

    this.studentService.getStudentProfile(this.user.id).subscribe({
      next: (profile) => {
        this.studentProfile = profile;
        this.loading = false;
        console.log('Student profile loaded:', profile);
      },
      error: (error) => {
        console.error('Error loading student profile:', error);

        // Provide more specific error messages
        let errorMessage = 'Не удалось загрузить данные профиля';
        if (error.status === 404) {
          errorMessage = 'Профиль студента не найден';
        } else if (error.status === 403) {
          errorMessage = 'Доступ к данным профиля запрещен';
        } else if (error.status === 0) {
          errorMessage = 'Не удается подключиться к серверу';
        }

        this.error = errorMessage;
        this.loading = false;
        this.snackBar.open(errorMessage, 'Закрыть', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  getGpaColor(gpa: number): string {
    if (gpa >= 3.5) return 'text-green-600';
    if (gpa >= 3.0) return 'text-yellow-600';
    if (gpa >= 2.5) return 'text-orange-600';
    return 'text-red-600';
  }

  getProgressColor(progress: number): string {
    if (progress >= 75) return 'bg-green-500';
    if (progress >= 50) return 'bg-yellow-500';
    if (progress >= 25) return 'bg-orange-500';
    return 'bg-red-500';
  }

  getDegreeStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'suspended': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  truncateDescription(description: string, maxLength: number = 150): string {
    if (description.length <= maxLength) return description;
    return description.substring(0, maxLength) + '...';
  }

  refreshProfile() {
    if (this.user && this.user.id && this.user.role === 'student') {
      this.loadStudentProfile();
    }
  }

  navigateToCourse(threadId: number) {
    // This method can be used to navigate to course details
    console.log('Navigate to thread:', threadId);
  }

  registerForCourse(courseId: number) {
    // This method can be used to register for a course
    console.log('Register for course:', courseId);
  }
}
