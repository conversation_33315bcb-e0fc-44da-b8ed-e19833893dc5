import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../core/services/auth.service';
import { ThreadService } from '../../core/services/thread.service';

interface CourseRating {
  id: number;
  title: string;
  code: string;
  score: number;
  current: number;
  letterGrade: string;
  gpa: number;
  credits: number;
  assignments: AssignmentRating[];
}

interface AssignmentRating {
  id: number;
  title: string;
  score: number;
  maxScore: number;
  weight: number;
  date: string;
}

interface SemesterRating {
  id: number;
  name: string;
  gpa: number;
  credits: number;
}

interface RegisteredCourse {
  id: number;
  thread: any;
  course: any;
}

interface CourseGrade {
  thread_id: number;
  course_name: string;
  final_grade?: number;
}

interface CourseGradesResponse {
  grades: CourseGrade[];
}

@Component({
  selector: 'app-ratings',
  templateUrl: './ratings.component.html',
  styleUrl: './ratings.component.css'
})
export class RatingsComponent implements OnInit {
  // User info
  user: any;
  isTeacher: boolean = false;

  // UI state
  activeTab: 'current' | 'history' | 'details' = 'current';
  selectedCourse: CourseRating | null = null;
  selectedSemester: string = 'current';
  isLoading: boolean = false;

  // Student registered courses
  registeredCourses: any[] = [];
  isLoadingRegisteredCourses: boolean = false;

  // Course grades data
  courseGrades: CourseGrade[] = [];
  isLoadingCourseGrades: boolean = false;

  // Breadcrumbs
  breadcrumbs: { label: string, url?: string }[] = [
    { label: 'Главная', url: '/' },
    { label: 'Оценки' },
  ];

  // Data
  courses: CourseRating[] = [
    {
      id: 1,
      title: 'Marketing Digitale Avanzato',
      code: 'MKT301',
      score: 90,
      current: 0,
      letterGrade: 'A-',
      gpa: 3.7,
      credits: 3,
      assignments: [
        { id: 1, title: 'Midterm Exam', score: 85, maxScore: 100, weight: 30, date: '2023-10-15' },
        { id: 2, title: 'Final Project', score: 92, maxScore: 100, weight: 40, date: '2023-12-10' },
        { id: 3, title: 'Case Study', score: 88, maxScore: 100, weight: 20, date: '2023-11-05' },
        { id: 4, title: 'Participation', score: 95, maxScore: 100, weight: 10, date: '2023-12-15' }
      ]
    },
    {
      id: 2,
      title: 'UI/UX Design',
      code: 'DES202',
      score: 75,
      current: 0,
      letterGrade: 'C+',
      gpa: 2.3,
      credits: 4,
      assignments: [
        { id: 5, title: 'Design Portfolio', score: 78, maxScore: 100, weight: 50, date: '2023-12-01' },
        { id: 6, title: 'Usability Testing', score: 72, maxScore: 100, weight: 30, date: '2023-11-15' },
        { id: 7, title: 'Wireframing Exercise', score: 75, maxScore: 100, weight: 20, date: '2023-10-20' }
      ]
    },
    {
      id: 3,
      title: 'Business Analytics',
      code: 'BUS305',
      score: 83,
      current: 0,
      letterGrade: 'B',
      gpa: 3.0,
      credits: 3,
      assignments: [
        { id: 8, title: 'Data Analysis Project', score: 85, maxScore: 100, weight: 40, date: '2023-11-25' },
        { id: 9, title: 'Midterm Exam', score: 80, maxScore: 100, weight: 30, date: '2023-10-10' },
        { id: 10, title: 'Case Presentations', score: 84, maxScore: 100, weight: 30, date: '2023-12-05' }
      ]
    },
    {
      id: 4,
      title: 'Artificial Intelligence',
      code: 'CS401',
      score: 48,
      current: 0,
      letterGrade: 'F',
      gpa: 0.0,
      credits: 4,
      assignments: [
        { id: 11, title: 'Algorithm Implementation', score: 45, maxScore: 100, weight: 35, date: '2023-11-10' },
        { id: 12, title: 'Research Paper', score: 50, maxScore: 100, weight: 25, date: '2023-10-25' },
        { id: 13, title: 'Final Exam', score: 48, maxScore: 100, weight: 40, date: '2023-12-15' }
      ]
    },
    {
      id: 5,
      title: 'Django Framework',
      code: 'CS310',
      score: 88,
      current: 0,
      letterGrade: 'B+',
      gpa: 3.3,
      credits: 3,
      assignments: [
        { id: 14, title: 'Web Application Project', score: 90, maxScore: 100, weight: 50, date: '2023-12-10' },
        { id: 15, title: 'Code Review', score: 85, maxScore: 100, weight: 25, date: '2023-11-15' },
        { id: 16, title: 'API Design', score: 88, maxScore: 100, weight: 25, date: '2023-10-30' }
      ]
    },
    {
      id: 6,
      title: 'Project Management',
      code: 'MGT320',
      score: 92,
      current: 0,
      letterGrade: 'A-',
      gpa: 3.7,
      credits: 3,
      assignments: [
        { id: 17, title: 'Project Plan', score: 94, maxScore: 100, weight: 30, date: '2023-10-20' },
        { id: 18, title: 'Team Leadership', score: 90, maxScore: 100, weight: 30, date: '2023-11-25' },
        { id: 19, title: 'Final Presentation', score: 92, maxScore: 100, weight: 40, date: '2023-12-15' }
      ]
    }
  ];

  semesters: SemesterRating[] = [
    { id: 1, name: 'Fall 2023', gpa: 3.2, credits: 20 },
    { id: 2, name: 'Spring 2023', gpa: 3.5, credits: 18 },
    { id: 3, name: 'Fall 2022', gpa: 3.1, credits: 19 },
    { id: 4, name: 'Spring 2022', gpa: 3.7, credits: 17 }
  ];

  // Computed properties
  get currentGPA(): number {
    const coursesWithGrades = this.getFormattedCourseGrades().filter(course => course.score > 0);
    if (coursesWithGrades.length === 0) return 0;

    const totalCredits = coursesWithGrades.reduce((sum, course) => sum + course.credits, 0);
    const weightedGPA = coursesWithGrades.reduce((sum, course) => sum + (course.gpa * course.credits), 0);

    return totalCredits > 0 ? parseFloat((weightedGPA / totalCredits).toFixed(2)) : 0;
  }

  get totalCredits(): number {
    const coursesWithGrades = this.getFormattedCourseGrades().filter(course => course.score > 0);
    return coursesWithGrades.reduce((sum, course) => sum + course.credits, 0);
  }

  constructor(
    private authService: AuthService,
    private threadService: ThreadService
  ) {}

  ngOnInit() {
    // Get user info
    this.user = this.authService.getCurrentUser();
    this.isTeacher = this.user?.role === 'teacher';

    // For teachers, also fetch course grades to display their teaching performance
    // For students, redirect is removed to allow them to see their grades

    // Fetch course grades for all users
    if (this.user && this.user.id) {
      this.fetchCourseGrades();

      // Also fetch registered courses for students
      if (!this.isTeacher) {
        this.fetchRegisteredCourses();
      }
    }

    // Animate progress circles will be called after course grades are loaded
  }

  /**
   * Fetch the student's registered courses
   */
  fetchRegisteredCourses() {
    if (!this.user || !this.user.id) return;

    this.isLoadingRegisteredCourses = true;

    this.threadService.getThreadsWithScheduleForUser(this.user.id).subscribe({
      next: (data: any) => {
        if (data && data.threads) {
          this.registeredCourses = data.threads;
        }
        this.isLoadingRegisteredCourses = false;
      },
      error: (err) => {
        console.error('Error fetching registered courses:', err);

        // Fallback to the old endpoint
        this.threadService.getListOfThreadsForUser(this.user.id).subscribe({
          next: (fallbackData: any) => {
            if (fallbackData && fallbackData.threads) {
              this.registeredCourses = fallbackData.threads;
            }
            this.isLoadingRegisteredCourses = false;
          },
          error: (fallbackErr) => {
            console.error('Error fetching registered courses (fallback):', fallbackErr);
            this.isLoadingRegisteredCourses = false;
          }
        });
      }
    });
  }

  /**
   * Fetch the user's course grades
   */
  fetchCourseGrades() {
    if (!this.user || !this.user.id) return;

    this.isLoadingCourseGrades = true;

    this.threadService.getUserCourseGrades(this.user.id).subscribe({
      next: (data: CourseGradesResponse) => {
        if (data && data.grades) {
          this.courseGrades = data.grades;
          console.log('Course grades loaded:', this.courseGrades);

          // Animate progress circles for courses with grades
          this.getFormattedCourseGrades().forEach(course => {
            this.animateProgress(course);
          });
        }
        this.isLoadingCourseGrades = false;
      },
      error: (err) => {
        console.error('Error fetching course grades:', err);
        this.courseGrades = [];
        this.isLoadingCourseGrades = false;
      }
    });
  }

  // Tab navigation
  setActiveTab(tab: 'current' | 'history' | 'details') {
    this.activeTab = tab;
  }

  // Select course for detailed view
  selectCourse(course: CourseRating) {
    this.selectedCourse = course;
    this.setActiveTab('details');
  }

  // Select semester for history view
  changeSemester(semesterId: string) {
    this.selectedSemester = semesterId;
    // In a real app, you would fetch data for the selected semester here
  }

  // Animation for progress circles
  animateProgress(course: CourseRating) {
    const stepTime = 7;
    const interval = setInterval(() => {
      if (course.current < course.score) {
        course.current += 1;
      } else {
        clearInterval(interval);
      }
    }, stepTime);
  }

  // Color coding for grades
  getColor(val: number): string {
    if (val < 50) return '#ef4444'; // red
    if (val < 80) return '#facc15'; // yellow
    return '#22c55e';              // green
  }

  // Get letter grade from percentage
  getLetterGrade(score: number): string {
    if (score >= 93) return 'A';
    if (score >= 90) return 'A-';
    if (score >= 87) return 'B+';
    if (score >= 83) return 'B';
    if (score >= 80) return 'B-';
    if (score >= 77) return 'C+';
    if (score >= 73) return 'C';
    if (score >= 70) return 'C-';
    if (score >= 67) return 'D+';
    if (score >= 65) return 'D';
    return 'F';
  }

  // Get GPA value from percentage
  getGPAValue(score: number): number {
    if (score >= 93) return 4.0;
    if (score >= 90) return 3.7;
    if (score >= 87) return 3.3;
    if (score >= 83) return 3.0;
    if (score >= 80) return 2.7;
    if (score >= 77) return 2.3;
    if (score >= 73) return 2.0;
    if (score >= 70) return 1.7;
    if (score >= 67) return 1.3;
    if (score >= 65) return 1.0;
    return 0.0;
  }

  // Back to course list from details view
  backToCourses() {
    this.selectedCourse = null;
    this.setActiveTab('current');
  }

  /**
   * Get course grades formatted as CourseRating objects
   */
  getFormattedCourseGrades(): CourseRating[] {
    return this.courseGrades.map(grade => ({
      id: grade.thread_id,
      title: grade.course_name,
      code: `THREAD-${grade.thread_id}`,
      score: grade.final_grade || 0,
      current: grade.final_grade || 0,
      letterGrade: this.getLetterGrade(grade.final_grade || 0),
      gpa: this.getGPAValue(grade.final_grade || 0),
      credits: 3, // Default credits, could be fetched from API if available
      assignments: [] // Could be populated if needed
    }));
  }

  /**
   * Check if a course has a final grade
   */
  hasFinalGrade(courseGrade: CourseGrade): boolean {
    return courseGrade.final_grade !== undefined && courseGrade.final_grade !== null;
  }

  /**
   * Get courses without final grades
   */
  getCoursesWithoutGrades(): CourseGrade[] {
    return this.courseGrades.filter(grade => !this.hasFinalGrade(grade));
  }

  /**
   * Get courses with final grades
   */
  getCoursesWithFinalGrades(): CourseGrade[] {
    return this.courseGrades.filter(grade => this.hasFinalGrade(grade));
  }
}
